#!/bin/bash

# 抖音底稿数据接口测试脚本
# 用于验证接口返回的数据结构

echo "🚀 抖音底稿数据接口测试"
echo "=========================="
echo ""

# 接口基础信息
BASE_URL="http://9gzj7t9k.fn.bytedance.net/api/search/stream"

# 测试查询列表
declare -a test_queries=(
    "九九乘法表"
    "三角函数"
    "勾股定理"
    "拼音字母表"
    "中国朝代顺序"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试单个查询
test_query() {
    local query="$1"
    local encoded_query=$(printf '%s' "$query" | jq -sRr @uri)
    local url="${BASE_URL}?query=${encoded_query}&summary_only=1"
    
    echo -e "${BLUE}📝 测试查询: ${query}${NC}"
    echo "🔗 请求URL: $url"
    echo ""
    
    # 发送请求并保存响应
    local response_file="/tmp/internal_data_response_$(date +%s).json"
    local http_code=$(curl -s -w "%{http_code}" -o "$response_file" "$url")
    
    echo "📊 HTTP状态码: $http_code"
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ 请求成功${NC}"
        
        # 检查响应是否为有效JSON
        if jq empty "$response_file" 2>/dev/null; then
            echo -e "${GREEN}✅ 响应为有效JSON${NC}"
            
            # 提取关键字段
            local answer=$(jq -r '.answer // "N/A"' "$response_file")
            local pv=$(jq -r '.pv // "N/A"' "$response_file")
            local reasoning=$(jq -r '.reasoning // "N/A"' "$response_file")
            local reference=$(jq -r '.reference // "N/A"' "$response_file")
            
            echo ""
            echo "📋 数据结构分析:"
            echo "  - answer字段: $([ "$answer" != "N/A" ] && echo -e "${GREEN}存在${NC}" || echo -e "${RED}缺失${NC}")"
            echo "  - pv字段: $([ "$pv" != "N/A" ] && echo -e "${GREEN}存在 ($pv)${NC}" || echo -e "${RED}缺失${NC}")"
            echo "  - reasoning字段: $([ "$reasoning" != "N/A" ] && echo -e "${GREEN}存在${NC}" || echo -e "${YELLOW}缺失${NC}")"
            echo "  - reference字段: $([ "$reference" != "N/A" ] && echo -e "${GREEN}存在${NC}" || echo -e "${RED}缺失${NC}")"
            
            # 显示answer内容摘要
            if [ "$answer" != "N/A" ]; then
                local answer_length=${#answer}
                local answer_preview=$(echo "$answer" | head -c 100)
                echo ""
                echo "📝 Answer内容摘要 (长度: $answer_length):"
                echo "  $answer_preview..."
                
                # 检查HTML标记
                if echo "$answer" | grep -q "<mark>"; then
                    echo -e "  ${GREEN}✅ 包含<mark>标记${NC}"
                fi
                
                # 检查特殊引用标记
                if echo "$answer" | grep -q "🔶[0-9]🔷"; then
                    echo -e "  ${GREEN}✅ 包含🔶n🔷引用标记${NC}"
                fi
            fi
            
            # 显示reference内容摘要
            if [ "$reference" != "N/A" ]; then
                local ref_lines=$(echo "$reference" | wc -l)
                echo ""
                echo "📚 Reference内容摘要 (行数: $ref_lines):"
                echo "$reference" | head -3 | sed 's/^/  /'
                if [ $ref_lines -gt 3 ]; then
                    echo "  ..."
                fi
            fi
            
        else
            echo -e "${RED}❌ 响应不是有效的JSON${NC}"
            echo "响应内容:"
            head -5 "$response_file" | sed 's/^/  /'
        fi
        
    else
        echo -e "${RED}❌ 请求失败${NC}"
        echo "错误响应:"
        cat "$response_file" | head -5 | sed 's/^/  /'
    fi
    
    # 清理临时文件
    rm -f "$response_file"
    
    echo ""
    echo "----------------------------------------"
    echo ""
}

# 测试接口连通性
test_connectivity() {
    echo -e "${BLUE}🔍 测试接口连通性${NC}"
    
    local test_url="${BASE_URL}?query=test&summary_only=1"
    local http_code=$(curl -s -w "%{http_code}" -o /dev/null "$test_url")
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ 接口连通正常${NC}"
        return 0
    else
        echo -e "${RED}❌ 接口连通失败 (HTTP: $http_code)${NC}"
        return 1
    fi
}

# 性能测试
performance_test() {
    echo -e "${BLUE}⚡ 性能测试${NC}"
    
    local query="九九乘法表"
    local encoded_query=$(printf '%s' "$query" | jq -sRr @uri)
    local url="${BASE_URL}?query=${encoded_query}&summary_only=1"
    
    echo "测试查询: $query"
    echo "测试次数: 5次"
    echo ""
    
    local total_time=0
    local success_count=0
    
    for i in {1..5}; do
        echo -n "第${i}次测试... "
        
        local start_time=$(date +%s%3N)
        local http_code=$(curl -s -w "%{http_code}" -o /dev/null "$url")
        local end_time=$(date +%s%3N)
        
        local duration=$((end_time - start_time))
        
        if [ "$http_code" -eq 200 ]; then
            echo -e "${GREEN}成功 (${duration}ms)${NC}"
            total_time=$((total_time + duration))
            success_count=$((success_count + 1))
        else
            echo -e "${RED}失败 (HTTP: $http_code)${NC}"
        fi
    done
    
    if [ $success_count -gt 0 ]; then
        local avg_time=$((total_time / success_count))
        echo ""
        echo "📊 性能统计:"
        echo "  - 成功率: $success_count/5 ($(($success_count * 20))%)"
        echo "  - 平均响应时间: ${avg_time}ms"
        echo "  - 总耗时: ${total_time}ms"
    fi
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ curl 命令未找到，请先安装 curl${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}❌ jq 命令未找到，请先安装 jq${NC}"
        exit 1
    fi
    
    # 测试接口连通性
    if ! test_connectivity; then
        echo -e "${RED}❌ 接口连通性测试失败，终止测试${NC}"
        exit 1
    fi
    
    echo ""
    echo "🧪 开始数据结构测试"
    echo "===================="
    echo ""
    
    # 测试各个查询
    for query in "${test_queries[@]}"; do
        test_query "$query"
    done
    
    # 性能测试
    performance_test
    
    echo ""
    echo -e "${GREEN}🎉 所有测试完成！${NC}"
    echo ""
    echo "💡 使用建议:"
    echo "  1. 确保answer字段存在且内容丰富"
    echo "  2. 利用pv字段显示内容热度"
    echo "  3. 保持HTML标记和特殊引用标记的完整性"
    echo "  4. 合理利用reference字段中的来源信息"
}

# 运行主函数
main "$@"
