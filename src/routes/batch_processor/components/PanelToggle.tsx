import React from 'react';
import Icon from './Icon';
import Tooltip from './Tooltip';

/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🔄 PANEL TOGGLE - 面板折叠切换组件
 * ═══════════════════════════════════════════════════════════════════════════════
 */

interface PanelToggleProps {
  /** 是否已折叠 */
  collapsed: boolean;
  /** 切换回调 */
  onToggle: () => void;
  /** 面板位置 */
  position: 'left' | 'right';
  /** 工具提示内容 */
  tooltip?: string;
  /** 额外的CSS类名 */
  className?: string;
}

export const PanelToggle: React.FC<PanelToggleProps> = ({
  collapsed,
  onToggle,
  position,
  tooltip,
  className = '',
}) => {
  const defaultTooltip = collapsed
    ? `展开${position === 'left' ? '左' : '右'}侧面板`
    : `折叠${position === 'left' ? '左' : '右'}侧面板`;

  const iconType =
    position === 'left'
      ? collapsed
        ? 'arrow-right'
        : 'arrow-left'
      : collapsed
        ? 'arrow-left'
        : 'arrow-right';

  return (
    <Tooltip content={tooltip || defaultTooltip} position="bottom">
      <button
        onClick={onToggle}
        className={`panel-toggle ${className}`}
        aria-label={tooltip || defaultTooltip}
      >
        <Icon type={iconType as any} color="primary" size="sm" />
      </button>
    </Tooltip>
  );
};

/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE LAYOUT WRAPPER - 响应式布局包装器
 * ═══════════════════════════════════════════════════════════════════════════════
 */

interface ResponsiveLayoutWrapperProps {
  children: React.ReactNode;
  sidebarCollapsed: boolean;
  consoleCollapsed: boolean;
  layoutMode: 'normal' | 'processing' | 'focus' | 'fullscreen';
  onToggleSidebar: () => void;
  onToggleConsole: () => void;
  isMobile: boolean;
  isTablet: boolean;
}

export const ResponsiveLayoutWrapper: React.FC<
  ResponsiveLayoutWrapperProps
> = ({
  children,
  sidebarCollapsed,
  consoleCollapsed,
  layoutMode,
  onToggleSidebar,
  onToggleConsole,
  isMobile,
  isTablet,
}) => {
  const layoutClassName = [
    'batch-processor-layout',
    layoutMode !== 'normal' && `${layoutMode}-mode`,
    sidebarCollapsed && 'sidebar-collapsed',
    consoleCollapsed && 'console-collapsed',
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={layoutClassName}>
      {/* 侧边栏切换按钮 - 仅在桌面端显示 */}
      {!isMobile && layoutMode !== 'fullscreen' && (
        <PanelToggle
          collapsed={sidebarCollapsed}
          onToggle={onToggleSidebar}
          position="left"
          className="sidebar-toggle"
        />
      )}

      {/* 控制台切换按钮 - 在平板和桌面端显示 */}
      {!isMobile && layoutMode !== 'fullscreen' && layoutMode !== 'focus' && (
        <PanelToggle
          collapsed={consoleCollapsed}
          onToggle={onToggleConsole}
          position="right"
          className="console-toggle"
        />
      )}

      {children}
    </div>
  );
};

/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎛️ LAYOUT MODE SWITCHER - 布局模式切换器
 * ═══════════════════════════════════════════════════════════════════════════════
 */

interface LayoutModeSwitcherProps {
  currentMode: 'normal' | 'processing' | 'focus' | 'fullscreen';
  onModeChange: (
    mode: 'normal' | 'processing' | 'focus' | 'fullscreen',
  ) => void;
  isMobile: boolean;
}

export const LayoutModeSwitcher: React.FC<LayoutModeSwitcherProps> = ({
  currentMode,
  onModeChange,
  isMobile,
}) => {
  const modes = [
    {
      key: 'normal' as const,
      label: '标准',
      icon: 'grid',
      tooltip: '标准三栏布局',
    },
    {
      key: 'processing' as const,
      label: '处理',
      icon: 'processing',
      tooltip: '处理模式 - 更多空间给主内容',
    },
    {
      key: 'focus' as const,
      label: '专注',
      icon: 'lightbulb',
      tooltip: '专注模式 - 隐藏左侧面板',
    },
    {
      key: 'fullscreen' as const,
      label: '全屏',
      icon: 'external-link',
      tooltip: '全屏模式 - 只显示主内容',
    },
  ];

  // 移动端只显示部分模式
  const availableModes = isMobile
    ? modes.filter(mode => ['normal', 'fullscreen'].includes(mode.key))
    : modes;

  return (
    <div className="layout-mode-switcher flex bg-gray-100 rounded-lg p-1">
      {availableModes.map(mode => (
        <Tooltip key={mode.key} content={mode.tooltip} position="top">
          <button
            onClick={() => onModeChange(mode.key)}
            className={`layout-mode-btn px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
              currentMode === mode.key
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Icon
              type={mode.icon as any}
              color={currentMode === mode.key ? 'primary' : 'gray'}
              size="sm"
            />
            {!isMobile && <span>{mode.label}</span>}
          </button>
        </Tooltip>
      ))}
    </div>
  );
};

export default PanelToggle;
