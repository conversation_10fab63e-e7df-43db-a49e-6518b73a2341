/**
 * 底稿数据功能演示组件
 * 
 * 功能：
 * 1. 演示模式切换功能
 * 2. 展示底稿数据处理流程
 * 3. 提供测试用例和示例
 * 4. 显示性能对比
 */

import React, { useState, useCallback, useEffect } from 'react';
import { ModeToggle } from './ModeToggle';
import { DataSourceErrorHandler } from './DataSourceErrorHandler';
import { DataProcessingService, QueryProcessingResult } from '../services/DataProcessingService';
import { InternalDataService, InternalDataError } from '../services/InternalDataService';
import Icon from './Icon';

/**
 * 演示组件
 */
export const InternalDataDemo: React.FC = () => {
  // 状态管理
  const [useInternalData, setUseInternalData] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [testQuery, setTestQuery] = useState('九九乘法表');
  const [processingResult, setProcessingResult] = useState<QueryProcessingResult | null>(null);
  const [error, setError] = useState<InternalDataError | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [cacheStats, setCacheStats] = useState<any>(null);

  // 测试查询列表
  const testQueries = [
    '九九乘法表',
    '三角函数',
    '勾股定理',
    '拼音字母表',
    '中国朝代顺序',
    '元素周期表',
    '世界各国GDP排名',
    '二十四节气',
  ];

  // 模式切换处理
  const handleModeToggle = useCallback((enabled: boolean) => {
    setUseInternalData(enabled);
    setError(null);
    setErrorMessage('');
    setProcessingResult(null);
  }, []);

  // 测试查询处理
  const handleTestQuery = useCallback(async () => {
    if (!testQuery.trim()) {
      return;
    }

    setIsLoading(true);
    setError(null);
    setErrorMessage('');
    setProcessingResult(null);

    try {
      const result = await DataProcessingService.processQuery(
        testQuery,
        useInternalData,
        (progress) => {
          console.log(`处理进度: ${Math.round(progress * 100)}%`);
        }
      );

      setProcessingResult(result);
      console.log('查询处理完成:', result);

    } catch (err) {
      console.error('查询处理失败:', err);
      setError(InternalDataError.NETWORK_ERROR);
      setErrorMessage(err instanceof Error ? err.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  }, [testQuery, useInternalData]);

  // 获取缓存统计
  const updateCacheStats = useCallback(() => {
    const stats = InternalDataService.getCacheStats();
    setCacheStats(stats);
  }, []);

  // 清空缓存
  const handleClearCache = useCallback(() => {
    InternalDataService.clearCache();
    updateCacheStats();
  }, [updateCacheStats]);

  // 错误重试
  const handleRetry = useCallback(() => {
    handleTestQuery();
  }, [handleTestQuery]);

  // 回退到直接模式
  const handleFallback = useCallback(() => {
    setUseInternalData(false);
    setError(null);
    setErrorMessage('');
  }, []);

  // 定期更新缓存统计
  useEffect(() => {
    updateCacheStats();
    const interval = setInterval(updateCacheStats, 5000);
    return () => clearInterval(interval);
  }, [updateCacheStats]);

  return (
    <div className="internal-data-demo">
      <div className="demo-container">
        {/* 标题 */}
        <div className="demo-header">
          <h2 className="demo-title">
            <Icon type="database" size="md" className="mr-2" />
            底稿数据功能演示
          </h2>
          <p className="demo-description">
            测试和演示抖音内部底稿数据的集成功能，包括模式切换、数据处理和错误处理。
          </p>
        </div>

        {/* 模式切换 */}
        <div className="demo-section">
          <h3 className="demo-section-title">模式切换</h3>
          <ModeToggle
            useInternalData={useInternalData}
            onToggle={handleModeToggle}
            loading={isLoading}
            showDescription={true}
          />
        </div>

        {/* 测试查询 */}
        <div className="demo-section">
          <h3 className="demo-section-title">测试查询</h3>
          <div className="test-query-container">
            <div className="test-query-input">
              <input
                type="text"
                value={testQuery}
                onChange={(e) => setTestQuery(e.target.value)}
                placeholder="输入测试查询..."
                className="form-input"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={handleTestQuery}
                disabled={isLoading || !testQuery.trim()}
                className="btn btn-primary"
              >
                {isLoading ? (
                  <>
                    <Icon type="loading" size="sm" className="animate-spin mr-2" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Icon type="play" size="sm" className="mr-2" />
                    测试查询
                  </>
                )}
              </button>
            </div>

            {/* 快速测试按钮 */}
            <div className="quick-test-buttons">
              <span className="quick-test-label">快速测试:</span>
              {testQueries.map((query) => (
                <button
                  key={query}
                  type="button"
                  onClick={() => setTestQuery(query)}
                  className="quick-test-btn"
                  disabled={isLoading}
                >
                  {query}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 错误处理 */}
        {error && (
          <div className="demo-section">
            <h3 className="demo-section-title">错误处理</h3>
            <DataSourceErrorHandler
              error={error}
              errorMessage={errorMessage}
              query={testQuery}
              onRetry={handleRetry}
              onFallback={handleFallback}
              showDetails={true}
            />
          </div>
        )}

        {/* 处理结果 */}
        {processingResult && (
          <div className="demo-section">
            <h3 className="demo-section-title">处理结果</h3>
            <div className="processing-result">
              <div className="result-summary">
                <div className="result-item">
                  <span className="result-label">数据源:</span>
                  <span className={`result-value ${processingResult.source === 'internal' ? 'text-blue-600' : 'text-gray-600'}`}>
                    {processingResult.source === 'internal' ? '底稿数据' : '直接AI生成'}
                  </span>
                </div>
                <div className="result-item">
                  <span className="result-label">原始查询:</span>
                  <span className="result-value">{processingResult.originalQuery}</span>
                </div>
                <div className="result-item">
                  <span className="result-label">处理时间:</span>
                  <span className="result-value">
                    {new Date(processingResult.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                {processingResult.internalDataSummary && (
                  <div className="result-item">
                    <span className="result-label">数据摘要:</span>
                    <span className="result-value">{processingResult.internalDataSummary}</span>
                  </div>
                )}
                {processingResult.context && processingResult.context.pv && (
                  <div className="result-item">
                    <span className="result-label">数据热度:</span>
                    <span className="result-value text-orange-600">
                      {processingResult.context.pv} 次浏览
                    </span>
                  </div>
                )}
              </div>

              <div className="result-content">
                <h4 className="result-content-title">处理后的查询内容:</h4>
                <div className="result-content-text">
                  {processingResult.processedQuery.length > 500 
                    ? `${processingResult.processedQuery.substring(0, 500)}...` 
                    : processingResult.processedQuery
                  }
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 缓存统计 */}
        {cacheStats && (
          <div className="demo-section">
            <h3 className="demo-section-title">缓存统计</h3>
            <div className="cache-stats">
              <div className="cache-stats-summary">
                <div className="cache-stat-item">
                  <span className="cache-stat-label">缓存项数:</span>
                  <span className="cache-stat-value">{cacheStats.size}</span>
                </div>
                <div className="cache-stat-item">
                  <span className="cache-stat-label">内存使用:</span>
                  <span className="cache-stat-value">
                    {Math.round(cacheStats.totalMemoryUsage / 1024)} KB
                  </span>
                </div>
              </div>
              
              <div className="cache-actions">
                <button
                  type="button"
                  onClick={updateCacheStats}
                  className="btn btn-sm btn-secondary"
                >
                  <Icon type="refresh-cw" size="xs" className="mr-1" />
                  刷新统计
                </button>
                <button
                  type="button"
                  onClick={handleClearCache}
                  className="btn btn-sm btn-secondary"
                  disabled={cacheStats.size === 0}
                >
                  <Icon type="trash-2" size="xs" className="mr-1" />
                  清空缓存
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InternalDataDemo;
